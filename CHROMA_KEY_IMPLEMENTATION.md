# Chroma Key Background Removal Implementation

This document describes the implementation of chroma key (green screen) background removal for the HeyGen avatar in your interactive avatar application.

## Overview

The chroma key implementation allows you to remove the green background from your HeyGen avatar video stream in real-time, making it appear as if the avatar has a transparent background. This is useful for overlaying the avatar on custom backgrounds or integrating it seamlessly into your application's UI.

## Implementation Details

### Files Modified

1. **`components/AvatarSession/AvatarVideo.tsx`**
   - Added chroma key processing logic
   - Added canvas element for processed video output
   - Added props interface for background removal control

2. **`components/InteractiveAvatar.tsx`**
   - Added state management for background removal toggle
   - Added UI toggle button for enabling/disabling background removal
   - Updated AvatarVideo component to pass removeBG prop

### Key Features

- **Real-time Processing**: Uses Canvas API with `requestAnimationFrame` for smooth real-time processing
- **Configurable Threshold**: Green detection threshold can be adjusted (currently set to 90)
- **Toggle Control**: Easy-to-use button to enable/disable background removal
- **Performance Optimized**: Efficient pixel manipulation with proper cleanup

### How It Works

1. **Video Capture**: The original video stream from HeyGen is captured in a `<video>` element
2. **Canvas Processing**: When background removal is enabled, each frame is drawn to a canvas
3. **Pixel Analysis**: Each pixel is analyzed for green color values using RGB thresholds
4. **Alpha Channel Manipulation**: Pixels matching green criteria have their alpha channel set to 0 (transparent)
5. **Display**: The processed canvas is displayed instead of the original video

### Green Detection Algorithm

```typescript
const isCloseToGreen = (color: number[]) => {
  const [red, green, blue] = color;
  const threshold = 90; // Adjustable threshold
  return green > threshold && red < threshold && blue < threshold;
};
```

The algorithm identifies green pixels by checking if:
- Green channel value > threshold (90)
- Red channel value < threshold (90)  
- Blue channel value < threshold (90)

## Usage

### Basic Usage

The background removal feature is automatically available in your avatar interface:

1. Start your avatar session
2. Look for the eye icon button in the top-right corner of the avatar video
3. Click the button to toggle background removal on/off
4. The icon changes to indicate the current state:
   - Eye icon (👁️): Background removal disabled
   - Eye-slash icon (👁️‍🗨️): Background removal enabled

### Customization

#### Adjusting the Green Detection Threshold

To modify the sensitivity of green detection, edit the threshold value in `AvatarVideo.tsx`:

```typescript
const isCloseToGreen = (color: number[]) => {
  const [red, green, blue] = color;
  const threshold = 90; // Increase for less sensitive, decrease for more sensitive
  return green > threshold && red < threshold && blue < threshold;
};
```

#### Styling the Toggle Button

The toggle button can be customized by modifying the styles in `InteractiveAvatar.tsx`:

```typescript
style={{
  width: "2.5rem",
  height: "2.5rem",
  borderRadius: "var(--radius-md)",
  border: "1px solid var(--border-light)",
  backgroundColor: removeBG ? "var(--primary-color)" : "var(--bg-primary)",
  color: removeBG ? "white" : "var(--text-secondary)",
  // Add your custom styles here
}}
```

## Performance Considerations

- **Frame Rate**: The processing runs at the video's frame rate using `requestAnimationFrame`
- **Memory Usage**: Canvas operations are optimized with `willReadFrequently: true`
- **CPU Usage**: Pixel manipulation is performed efficiently in a single loop
- **Cleanup**: Animation frames are properly cancelled when component unmounts

## Browser Compatibility

- **Modern Browsers**: Works in all modern browsers that support Canvas API
- **Mobile Devices**: Compatible with mobile browsers
- **WebRTC**: Requires WebRTC support for video streaming

## Troubleshooting

### Common Issues

1. **Background not being removed properly**
   - Adjust the threshold value (try values between 70-120)
   - Ensure adequate lighting on the green background
   - Check that the background is a pure green color

2. **Performance issues**
   - Reduce video quality if needed
   - Check browser performance tools for bottlenecks

3. **Toggle button not working**
   - Check browser console for JavaScript errors
   - Verify that the state is being updated correctly

### Testing

A test component (`ChromaKeyTest.tsx`) is included to help test the chroma key functionality with your local camera:

```typescript
import { ChromaKeyTest } from './components/ChromaKeyTest';

// Use in your application for testing
<ChromaKeyTest threshold={90} />
```

## Future Enhancements

Potential improvements that could be added:

1. **Advanced Color Detection**: Support for other background colors (blue screen, etc.)
2. **Edge Smoothing**: Anti-aliasing for smoother edges
3. **Multiple Threshold Values**: Different thresholds for different color channels
4. **Background Replacement**: Option to replace background with custom images/videos
5. **Performance Optimization**: WebGL-based processing for better performance

## Technical Reference

### Dependencies

- React 18+
- Canvas API
- RequestAnimationFrame API

### Browser APIs Used

- `HTMLCanvasElement.getContext('2d')`
- `CanvasRenderingContext2D.getImageData()`
- `CanvasRenderingContext2D.putImageData()`
- `window.requestAnimationFrame()`
- `window.cancelAnimationFrame()`

This implementation provides a solid foundation for chroma key background removal that can be further customized based on your specific needs.
