{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx"}, "dependencies": {"@heygen/streaming-avatar": "^2.0.13", "ahooks": "^3.8.4", "axios": "^1.10.0", "clsx": "^2.1.1", "next": "^15.3.0", "openai": "^4.52.1", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.1", "@types/node": "20.5.7", "@types/react": "^19.0.1", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "typescript": "5.0.4"}}