.inputContainer {
  height: 50px;
  border: 1px solid #51515126 !important;
  border-radius: 10px !important;
}
.inputContainer:hover {
  border: 1px solid #1b84ff !important;
}
.inputContainer:focus {
  border: 1px solid #1b84ff !important;
}
.label {
  color: #515151;
}
.error {
  color: red;
  font-size: 13px;
}
/* Button */
.buttonContainer {
  background-color: #1b84ff;
  color: #fff;
  font-weight: 700;
  height: 50px;
}
.secondaryButton {
  border: 1px solid #fffefe1a !important;
  background-color: transparent !important;
  font-weight: 700 !important;
  height: 50px;
  border-radius: 10px !important;
}
.secondaryButton:hover {
  border: 1px solid #fffefe1a !important;
  background-color: transparent !important;
  font-weight: 700 !important;
  height: 50px;
}
