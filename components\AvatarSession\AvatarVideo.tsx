import React, { forwardRef, useEffect, useRef, useState } from "react";
import { ConnectionQuality } from "@heygen/streaming-avatar";

import { useConnectionQuality } from "../logic/useConnectionQuality";
import { useStreamingAvatarSession } from "../logic/useStreamingAvatarSession";
import { StreamingAvatarSessionState } from "../logic";
import { CloseIcon } from "../Icons";
import { Button } from "../Button";
import Texas from "../../public/Svg/texas.svg";
import Image from "next/image";
import style from "../../styles/commonStyle.module.css";

interface AvatarVideoProps {
  removeBG?: boolean;
}

export const AvatarVideo = forwardRef<HTMLVideoElement, AvatarVideoProps>(
  ({ removeBG = false }, ref) => {
    const { sessionState, stopAvatar, stream } = useStreamingAvatarSession();
    const { connectionQuality } = useConnectionQuality();

    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [isProcessing, setIsProcessing] = useState(false);

    const isLoaded =
      sessionState === StreamingAvatarSessionState.CONNECTED && stream !== null;

    // Chroma key processing effect
    useEffect(() => {
      if (!removeBG || !isLoaded || !ref || typeof ref === "function") return;

      const renderCanvas = () => {
        const video = ref.current;
        const canvas = canvasRef.current;

        if (
          !canvas ||
          !video ||
          video.videoWidth === 0 ||
          video.videoHeight === 0
        ) {
          return requestAnimationFrame(renderCanvas);
        }

        const ctx = canvas.getContext("2d", { willReadFrequently: true });
        if (!ctx) return;

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          const red = data[i];
          const green = data[i + 1];
          const blue = data[i + 2];

          if (isCloseToGreen([red, green, blue])) {
            data[i + 3] = 0; // Set alpha channel to 0 (transparent)
          }
        }

        ctx.putImageData(imageData, 0, 0);
        return requestAnimationFrame(renderCanvas);
      };

      const isCloseToGreen = (color: number[]) => {
        const [red, green, blue] = color;
        const threshold = 90; // Adjust the threshold values for green detection
        return green > threshold && red < threshold && blue < threshold;
      };

      setIsProcessing(true);
      const animationFrameId = renderCanvas();

      return () => {
        setIsProcessing(false);
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }
      };
    }, [removeBG, isLoaded, ref]);

    return (
      <>
        {connectionQuality !== ConnectionQuality.UNKNOWN && (
          <div
            className="absolute border-round-lg px-3 py-2"
            style={{
              top: "12px",
              left: "12px",
              backgroundColor: "#000000",
              color: "#ffffff",
            }}
          >
            Connection Quality: {connectionQuality}
          </div>
        )}

        {/* {isLoaded && (
        <Button
          className="absolute p-2 z-1"
          style={{
            top: "12px",
            right: "12px",
            backgroundColor: "rgba(63, 63, 70, 0.5)",
          }}
          onClick={stopAvatar}
          icon={<CloseIcon />}
        />
      )} */}
        {isLoaded && (
          <div style={{ position: "relative" }}>
            <Image
              src={Texas}
              alt="texas"
              className="relative"
              style={{ top: "25rem" }}
            />
            <video
              ref={ref}
              autoPlay
              playsInline
              className={style.videoContainer}
              style={{
                display: removeBG ? "none" : "block",
              }}
            >
              <track kind="captions" />
            </video>
            {removeBG && (
              <canvas
                ref={canvasRef}
                className={style.videoContainer}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  backgroundColor: "transparent",
                }}
              />
            )}
          </div>
        )}
        {!isLoaded && (
          <div
            className="w-full h-full flex align-items-center justify-content-center absolute"
            style={{ top: 0, left: 0 }}
          >
            Loading...
          </div>
        )}
      </>
    );
  }
);
AvatarVideo.displayName = "AvatarVideo";
