.normalText {
  color: #515151;
}
.blueText {
  color: #1b84ff;
}

.homeBlur {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.blurImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.blurOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.centerImage {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: auto;
  z-index: 1;
}

.chatCard {
  box-sizing: border-box;
  position: absolute;
  width: 80%;
  height: 70%;
  left: 10%;
  top: 8%;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(20px);
  border-radius: 20px;
}
.conversationContainer {
  width: 35%;
  border-right: 1px solid rgba(255, 255, 255, 0.5);
  padding: 2rem;
}
.chatRightContainer {
  border-radius: 0 !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
  background-color: transparent !important;
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
}
.messageInput {
  background-color: transparent !important;
  width: 100%;
  border: none !important;
  color: #fff !important;
}
.messageInput:focus {
  border-color: none !important;
  box-shadow: none !important;
  outline: none !important;
}
.messageInputContainer {
  display: flex;
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
}
.sendIcon {
  position: absolute;
  right: 1rem;
  top: 1rem;
}
.chatReceive {
  background-color: #3b3a32;
  color: #fff;
  border-radius: 20px;
  border: 1px solid #ffffff1a;
  padding: 1rem;
  width: 60%;
}
.chatSend {
  background-color: #182427;
  color: #fff;
  border-radius: 20px;
  border: 1px solid #ffffff1a;
  padding: 1rem;
  width: 60%;
  margin-left: auto;
}
.chatMessages {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem 0;
}
.micButton {
  background-color: transparent !important;
  border: none !important;
}
.micButton:hover:not(:disabled) {
  box-shadow: none !important;
  background-color: transparent !important;
  border: none !important;
}
.micButton:hover {
  box-shadow: none !important;
  background-color: transparent !important;
  border: none !important;
}
.micButton:focus {
  box-shadow: none !important;
  background-color: transparent !important;
  border: none !important;
}
.interruptButton {
  border: 1px solid #fffefe1a !important;
  background-color: transparent !important;
  color: #fff !important;
}
.interruptButton:focus {
  box-shadow: none !important;
  outline: none !important;
}
.interruptButton:hover:not(:disabled) {
  box-shadow: none !important;
  background-color: transparent !important;
  border: 1px solid #fffefe1a !important;
}
.interruptButton:hover {
  box-shadow: none !important;
  background-color: transparent !important;
  border: 1px solid #fffefe1a !important;
}
.videoContainer {
  width: 50%;
  height: 35%;
  object-fit: contain;
  z-index: 1;
  position: relative;
  top: -6rem;
  left: -5rem;
  display: flex;
  margin-right: auto;
}
